.slick-slide{
    width:fit-content;
}

.video_on{
background:#fff;
}
.video_off{
    background:rgb(255, 61, 113);
}
.owl-stage {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-box;
    /* display: box; */
    }
.owl-item{
    margin:0px 7px !important;
}

    .item{
        margin:0px !important;
    }
    .imageswitch{

    position: absolute;
    width: 100%;
    height: 100%;
    /* margin: auto; */
    z-index: 22;
    background: #fff;

    }

.imageswitch2{
    margin: 0;
    position: absolute;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

    .videotools{
        position: absolute;
        display: flex;
        bottom:0px;
        height: 31px;
    }

    .video_mute_option{
        -webkit-flex: auto;
        flex: auto;
        position: relative;
        left: 0;
        /* margin-top: 14px; */
        top: 0;
        margin: 0px 8px 4px 8px;
        max-width: -webkit-fit-content;
        max-width: -moz-fit-content;
        max-width: fit-content;

    }

    .video_name_option{
        position: relative;
    top: 0;
    flex: auto;
    left: 8px;
    text-transform: capitalize;
    }
@media only screen and (min-width:0px) and (max-width:650px){
    .relative-localvideo{
        /* bottom: 80px !important; */
    }

    .local_host.relative-localvideo{
        bottom: 145px !important;
    }
}

/* @media only screen and (min-width:651px) and (max-width:870px){
    .relative-localvideo{
        bottom: 15.5vh !important;
    }
}
@media only screen and (min-width:871px) and (max-width:950px){
    .relative-localvideo{
        bottom: 15vh !important;
    }
}
@media only screen and (min-width:950px) and (max-width:1004px){
    .relative-localvideo{
        bottom: 13.5vh !important;
    }
}

@media only screen and (min-width:1005px) and (max-width:1189px){
    .relative-localvideo{
        bottom: 12vh !important;
    }
} */


/* @media only screen and (min-width:1101px) and (max-width:1189px){
    .relative-localvideo{
        bottom: 12vh !important;
    }
} */

@media only screen and (min-width:641) and (max-width:10000px) {
  .sceneHide{
    display: none;
  }
  .sceneMviewBtn {
    display: none;
  }
  .realtimeControllerMobile {
    display: none;
  }
}

/* realtime controller */
@media only screen and (min-width:641px) and (max-width:768px) {
  .controllerStyle {
    /* left: 220px; */
  }
}

@media only screen and (min-width:0px) and (max-width:640px) {
  .controllerStyle {
     display: none !important;
  }
}

/* scene controlller */
@media only screen and (min-width:0px) and (max-width:640px) {
  .sceneMviewBtn {
    position: fixed;
    display: flex;
    flex-direction: column-reverse;
    right: 20px;
    bottom: 25px;
    background-color: #ffffff1a;
    gap: 10px;
    /* padding: 10px; */
    /* border-radius: 50%; */
  }

  .sceneController {
    display: flex;
    flex-direction: column-reverse;
  }
  .TimerControl{
      bottom: 0.5rem;
      left: 0.5rem;
    }
  .realtimeControllerMobile{
    display: flex;
    /* border: 1px solid red; */
  }
  .sidebarRight{
    right: 0px;
  }
}
.backdrop-blur {
  backdrop-filter: blur(6px);
}

.guest_video .relative-localvideo{
    position: absolute;
    right: 50px;
    bottom: 82px;

}

.sidebarRight{
  right: 10px;
}
.relative-localvideo{
  position: fixed;
  top: 0 ;
  left: 0 ;
  transform: translate3d(0px,0px,0px);

}


.relative-localvideo>.user-video{
  position: relative;
}



.localvideo-list{
    background: rgb(0, 0, 0);
}


.fixed-video{
    /* height: 135px;
    width: 226px; */
    position: relative;
    /* padding-bottom: 56.25%; */
    height: auto;
    width: 100%;
  }
  .fixed-video>.user-video{
    position: absolute;
  }
.skeleton-box {
    display: inline-block;
    height: 1em;
    position: relative;
    overflow: hidden;
    background-color: #dddbdd;
    width: 100%;
    height: 100%;
    border-radius: inherit;
}
.skeleton-box::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0));
    animation: shimmer 2s infinite;
    content: '';
}
@keyframes shimmer {
    100% {
        transform: translateX(100%);
   }
}
.loader-logo{
    padding-top: 32px;
    display: flex;
    flex-direction: row-reverse;
    float: left;
}
.loader-logo>img{
    content: var(--main-image);
}
.lock{
    width: 40px;
    height: 43px;
    position: absolute;
    bottom: 50%;

    padding: 7px;
    margin-left: 8px;
    border-radius: 3px;
    outline: none;
    border: none;
}

@media screen and (max-width: 350) {
    .project_card{
        width: 250px;
    }

  }
  .host-offline{
    color: #fff;
    height: 100%;
    width: fit-content;
    margin: auto;
    position: relative;
    bottom: 99%;
    left: 33%;
  }

.mobilesignout{
  width: 100px;
    height: 40px;
    border-radius: 4px;
    border: solid 1px var(--main-color);
    cursor: pointer;
    font-family: 'propvr';
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.14;
    letter-spacing: normal;
    text-align: center;
    color: var(--main-color);
    text-transform: uppercase;}


    @media screen and (min-width:991px ) {
        .mobilesignout{
            display: none;
        }
    }


    .roomname{
        color: #fff;
          width: fit-content;
          height: fit-content;
          background: #000000b8;
          z-index: 1;
          padding: 8px 16px;
          position: absolute;
          margin: 8px;
          margin-top:12px;
          flex: auto;
          border-radius: 8px;
          max-width: fit-content;
      }

      .roomname-lock{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        top: 0;
        position: absolute;
        margin: auto;
        width: 100%;

      }

      .guest-lock{
        flex: auto;
       z-index: 1;
      }
      .guest-lock>div{
        height: 100%;
        width: fit-content;
        height: fit-content;
        border-radius: 4px;
        display: flex;
        cursor: pointer;
        font-family: 'propvr';
        font-size: 14px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;

        line-height: 1.14;
        margin: 12px auto;
        letter-spacing: normal;
        padding: 11px;
        background: var(--main-color) !important;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
      }
      .guest-lock>div>div{
        height: fit-content;
        position: relative;
        margin: auto  12px;
    }

    @media screen and (max-width:607px ) {
        .guest-lock>div{
            margin: 12px 12px;
        }
    }


    .reseller-theme-image{
        content: var(--main-image);
    }
    .reseller-theme-white-image{
        content: var(--main-white-image);
    }

  .header-brand-image{
    background: var(--main-image) no-repeat;
    width: 100%;
    height: 100%;
    background-size: contain;
  }


  .svgicons-reseller{
      fill:var(--main-color)
  }

  .header-custom-logo{
    height: 100%;
    width: auto;
    max-width: 60px;
  }

  .admit_button{
    box-shadow: none;
  background: var(--main-color) !important;
  color: #fff;
  padding: 0;

  }
  .deny_button{
    border: 3px solid #FF3D71;
    background: #9e9e9e24;
    color: #FF3D71;
    width: fit-content !important;
  }
  .notification{
    position: fixed;
    bottom: 0;
    right: 0;
    margin: 10px;
    z-index: 10001;
  }
  .toaster  {
    position: fixed;
    top: 0;
    right: 0;
    margin: 10px;
    z-index: 20001;
  }

  @keyframes slide-in {
    0% {
      transform: translateX(100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-out {
    0% {
      transform: translateX(0);
      opacity: 1;
    }
    100% {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out forwards;
  }

  .animate-slide-out {
    animation: slide-out 0.3s ease-in forwards;
  }




  .onlinecheck:not(:checked),
  .onlinecheck:checked {
  position: absolute;
  opacity: 0.01;
}
.onlinecheck:not(:checked) + label,
.onlinecheck:checked + label {
  position: relative;
  padding-left: 4.5em;
  cursor: pointer;
}
.onlinecheck:not(:checked) + label:before,
.onlinecheck:checked + label:before,
.onlinecheck:not(:checked) + label:after,
.onlinecheck:checked + label:after {
  content: '';
  position: absolute;
}
.onlinecheck:not(:checked) + label:before,
.onlinecheck:checked + label:before {
  left: 0; top: -.1em;
  width: 4em; height: 1.85em;
  background: #DDDDDD;
  border-radius: 1em;
  transition: background-color .2s;
}
.onlinecheck:not(:checked) + label:after,
.onlinecheck:checked + label:after {
  width: 1.4em; height: 1.4em;
  transition: all .2s;
  border-radius: 50%;
  background: #7F8C9A;
  top: .12em; left: .24em;
}
.text {
  display: inline-block;
  vertical-align: middle;
}

/* on checked */
.onlinecheck:checked + label:before {
  background:var(--main-color) !important;
}
.onlinecheck:checked + label:after {
  background: #fff;
  left: 2.4em;
}

.onlinecheck:checked + label .ui,
.onlinecheck:not(:checked) + label .ui:before,
.onlinecheck:checked + label .ui:after {
  position: absolute;
  left: .6em;
  width: 6em;
  border-radius: 1em;
  font-size: .875em;
  font-weight: bold;
  transition: all .2s;
}
.onlinecheck:not(:checked) + label .ui:before {

  left: 2.35em;
  top: .2em;
}
.onlinecheck:checked + label .ui:after {

  top: .2em;
  left: .2em;
  color: #39D2B4;
}
.onlinecheck:focus + label:before {
  border: 1px dashed #777;
  box-sizing: border-box;
  margin-top: -1px;
}

.Active-for-Calls{
  float: right;
  font-family: inherit;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  letter-spacing: normal;
  color: #000;
  width: inherit;
  margin: 8px 16px;
  height: -webkit-fill-available;
}

.rdrInputRanges{
  display :none;
}
.rdrDateRangePickerWrapper{
  display: flex;
}
.rdrMonth{
  width: inherit;
}

@media  only screen and(max-width:489px){
  .rdrCalendarWrapper{
        transform: scale(0.8) translateX(-50px);
  }
  .rdrDateRangePickerWrapper{
  flex-wrap: wrap;
  }
  .calendar_modal{
  transform: scale(0.7);
  }
  }

  .record-display-div{
    position: fixed;
     display: flex;
     margin-left:9px;
    align-items: center;
    height: 36px;
    padding: 0 12px 0 8px;
    justify-content: space-around;
    color: #fff;
    right: 8px;
    background: rgba(217,48,37,0.8);
    border-radius: 4px;
  }
  .record-display-div .circle{
    background-color: #fff;
    border-radius: 50%;
    height: 14px;
    width: 14px;
    margin: 3px;
  }

 .record-display-div .REC{
    font-family: 'Open Sans';
    font-weight: 500;
    margin-left: 4px;
    white-space: nowrap;
  }


  .highlighter{
    top: 157px;
    left: 103px;
    background: red;
    border-radius: 10px;
    width: 8px;
    height: 8px;
    transition: all 0.01s ease;
  }

  #renderpdf{
    height: 100% !important;
  }
  .stopRecording_icon{
    width: 24px;
    height: 24px;
    background: #fff;
    border: 2px solid red;
    border-radius: 15px;
  }

 .stopRecording_icon div {
    width: 10px;
    height: 10px;
    background: red;
    margin: auto;
    top: 4.5px;
    position: relative;
  }


  .tool_annotate{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    overflow: hidden;
    outline: 0;
    width: 100%;
    height: fit-content;
  }

  .tool_annotate div{
    position: relative;
    background: none;
box-shadow: 0px 0px !important;

  }

  @media (min-width: 576px)
{
.tool_annotate div {
    max-width: 500px;
    margin: 0px auto;
}}


.tools_annotate_icon{
  background: #fff;
  width: 90px;
  height: 37px;
    padding: 0px;
border-radius: 0px 0px 8px 8px;
  }

  .tools_annotate_icon div{
    width: 32px;
  }
  .tool_content .modal-header{
    display: none;
    border-radius: 0px 0px 5px 5px;
  }
  .tool_content .modal-body{
    border-radius: 0px 0px 5px 5px;

  }
  /* .tool_content:hover div{
   display: block;
   background-color: #fff;
  } */

  .tool_annotate_body{
    padding-bottom:0px;
  }
  .loader-1 {
    height: 32px;
    width: 32px;
    -webkit-animation: loader-1-1 4.8s linear infinite;
            animation: loader-1-1 4.8s linear infinite;
  }
  @-webkit-keyframes loader-1-1 {
    0%   { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }
  @keyframes loader-1-1 {
    0%   { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .loader-1 span {
    display: block;
    position: absolute;
    top: 0; left: 0;
    bottom: 0; right: 0;
    margin: auto;
    height: 32px;
    width: 32px;
    clip: rect(0, 32px, 32px, 16px);
    -webkit-animation: loader-1-2 1.2s linear infinite;
            animation: loader-1-2 1.2s linear infinite;
  }
  @-webkit-keyframes loader-1-2 {
    0%   { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(220deg); }
  }
  @keyframes loader-1-2 {
    0%   { transform: rotate(0deg); }
    100% { transform: rotate(220deg); }
  }
  .loader-1 span::after {
    content: "";
    position: absolute;
    top: 0; left: 0;
    bottom: 0; right: 0;
    margin: auto;
    height: 32px;
    width: 32px;
    clip: rect(0, 32px, 32px, 16px);
    border: 3px solid #2979FF;
    border-radius: 50%;
    -webkit-animation: loader-1-3 1.2s cubic-bezier(0.770, 0.000, 0.175, 1.000) infinite;
            animation: loader-1-3 1.2s cubic-bezier(0.770, 0.000, 0.175, 1.000) infinite;
  }
  @-webkit-keyframes loader-1-3 {
    0%   { -webkit-transform: rotate(-140deg); }
    50%  { -webkit-transform: rotate(-160deg); }
    100% { -webkit-transform: rotate(140deg); }
  }
  @keyframes loader-1-3 {
    0%   { transform: rotate(-140deg); }
    50%  { transform: rotate(-160deg); }
    100% { transform: rotate(140deg); }
  }
  .cancel-btn{
    background:#FF3D71;
  }
  .cancel-btn:hover{
    background: #FF3D71FF;
  }

  .banner .content{
    font-family: 'Open Sans';
    font-size: 30px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.33;
    letter-spacing: normal;
    color: #222b45;

}

  .input-field{
    margin-right: 28px;
    width: 298px;
    height: 40px;
    /* border-radius: 4px; */
    border: none;
    text-align: left!important;
    padding: 12px;
    /* margin-top: -26px; */
    /* background-color: #edf1f7; */
    background: #edf1f7; ;

  }
  .lds-ring {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
  }
  .lds-ring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 4px solid #000000;
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #000000 transparent transparent transparent;
  }
  .lds-ring div:nth-child(1) {
    animation-delay: -0.45s;
  }
  .lds-ring div:nth-child(2) {
    animation-delay: -0.3s;
  }
  .lds-ring div:nth-child(3) {
    animation-delay: -0.15s;
  }
  @keyframes lds-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .main_video_row{
    margin: auto;
    width: 100%;
    position: absolute;
    top: 72px;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 10%;
    align-content: center;
  }

  .video_heading{
    font-size: 1.95rem;
  font-weight: 400;
  padding-bottom: 30px;
  }

  .video_subheading{
    font-size: .875rem;
    padding-bottom: 20px;
  }

  .join_now_video{
    padding: 0 24px;
    background: #3366ff;
    color: #fff;
    cursor: pointer;
    text-align: center;
    border: 0;
    border-radius: 24px;
    box-shadow: 0px 1px 2px 0px rgb(60 64 67 / 30%), 0px 1px 3px 1px rgb(60 64 67 / 15%);
    display: flex;
    height: 48px;
    align-items: center;
  }


  .microphone_div{
    display: flex;
  justify-content: flex-end;
  margin-right: 10px;
  }

  .video_icon_div{
    height: 50px;
    width: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border:1px solid #fff;
  }

  .video_icon_div.selected{
    background-color: #d93025;
    border: 1px solid #d93025;
  }

  .video_icon_div.selected:hover{
    box-shadow: 0 1px 3px 0 rgb(60 64 67 / 30%), 0 4px 8px 3px rgb(60 64 67 / 15%);
    background-color: #d93025d1;
    border: 1px solid #d93025d1;
  }

  .video_icon_div:hover{
    background-color: rgba(255,255,255,0.4);
  }
  .INITVIDEO{
    margin: auto;
    height: 310px;
    border-radius: 10px;
  }
  .INITVIDEO video{
    width: 100%;
    border-radius: 10px;
    max-width: 650px;
    max-height: 400px;
    width: 100%;
    object-fit: cover;
    height: inherit;
  }
  .VideoOffname{

    color: #fff;
    margin: 0;
    position: relative;
    /* bottom: -80px; */
    word-break: break-word;
 flex:auto;
  }
  .VideoOffname span{
    position: relative;
    vertical-align:sub;

  }
  .AudioOffname{
    color: #fff;
    margin: 0;
    position: relative;
    word-break: break-word;
flex: auto;
max-height:30px;
width: fit-content;
    margin: 6px 8px;
  }
  .VideoControls_Div{
    bottom:0px;
    position: absolute;
    width: 100%;
    z-index: 1;
    height: fit-content;
    display: flex;
    flex-direction: row-reverse;
    margin-bottom: 6px ;
  }


  /* Screen sharing in TypeSwitch area - for default/no project sessions */
  .ScreenSharingInTypeSwitch{
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    padding: 0;
    left: 0 !important;
    top: 0 !important;
    z-index: 999 !important; /* Lower z-index to allow UI elements to show */
    background: #000;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  .Propvr_Embed{

  }
.screenshareVideo{
  position: relative;
  width: inherit;
  height: inherit;

}
  /* .a-canvas{
    width: inherit !important;
    height: inherit !important;
  } */
  /* a-scene{
    width: 500px  !important;
    height: 500px !important;
  } */


  .connectionStatus{
    margin-top: 13px;
    text-align: center;
    padding: 0 1%;
  }
  .connectionStatus span{
    width: fit-content;
  }
  .GOOD .connectionStatus{
    text-align: right;
    /* color: green;
    fill:green; */
  }

  .MODERATE .connectionStatus{
    text-align: center;
    /* color:orange;
    fill:orange; */
  }

  .POOR .connectionStatus{
    text-align: left;
    /* color:red;
    fill: red; */
  }
  .DEFAULT .connectionStatus{
    text-align: center;
    /* fill:#8F9BB3;
    color:#8F9BB3; */
  }

  .internetIcon{
    /* width: 100%; */
  }
  /* .internetIcon{
    margin-bottom: 4px;
  } */
  .GOOD .internetIcon {
    right: 20%;
    fill: #00C851;
    /* transform: translateX(-100%); */
  }

  .MODERATE .internetIcon{
     left: 50%;
     transform: translateX(-100%);
     fill: #FFBB33;
  }

  .POOR .internetIcon{
    left: 20%;
    fill: #FC6C36;
    /* transform: translateX(-100%); */
  }
  .DEFAULT .internetIcon{
    left: 50%;
    transform: translateX(-100%);
    fill: #FFBB33;
    /* border: 2px solid red; */
  }

  .LiveVisit_table{

  }
  .LiveVisit_table td{
    vertical-align: middle;
  }
  .a-enter-ar{
    visibility: hidden;
  }
  .Heading_h2{
    width: 100%;
   text-align: center;
   border-bottom: 1px solid #000;
   line-height: 0.1em;
   margin: 10px 0 20px;
  }
  .Heading_span{
    background:#fff;
    padding:0 10px;
    font-size: medium;
  }
  .css-1ex2uih-BaseToolTop{
    text-wrap:nowrap;
  }
  .custom-aspect-ratio{
    position: relative;
    width: 100%;
    padding-bottom: 56.25%;
    overflow: hidden;
  }



#react-root {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 10px;
}

section {
  margin-bottom: 50px;
}

a {
  display: inline-block;
}

.target {
  text-decoration: underline;
  cursor: pointer;
}

.target .react-tooltip-lite {
  cursor: default;
}

.flex-spread {
  display: flex;
  justify-content: space-between;
}

.tip-heading {
  margin: 0 0 10px;
}

.tip-list {
  margin: 0;
  padding: 0 0 0 15px;
}

.tip-list li {
  margin: 5px 0;
  padding: 0;
}

/* tooltip styles */
.react-tooltip-lite {
  background: #0000003d;
  color: white;
  font-weight: bold;
}

.react-tooltip-lite a {
  background: #0000003d;
  color: white;
  font-weight: bold;
}

.react-tooltip-lite a:hover {
  color: #4286f4;
}

.react-tooltip-lite-arrow {
  border-color: #0000003d;
}

/* overrides with a custom class */
.customTip .react-tooltip-lite {
  border: 1px solid #888;
  background: #ccc;
  color: black;
}

.customTip .react-tooltip-lite-arrow {
  border-color:  #0000003d;
  position: relative;
}

.customTip .react-tooltip-lite-arrow::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  z-index: 99;
  display: block;
}

.customTip .react-tooltip-lite-up-arrow::before {
  border-top: 10px solid #ccc;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  left: -10px;
  top: -11px;
}

.customTip .react-tooltip-lite-down-arrow::before {
  border-bottom: 10px solid #ccc;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  left: -10px;
  bottom: -11px;
}

.customTip .react-tooltip-lite-right-arrow::before {
  border-right: 10px solid #ccc;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  right: -11px;
  top: -10px;
}

.customTip .react-tooltip-lite-left-arrow::before {
  border-left: 10px solid #ccc;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  left: -11px;
  top: -10px;
}

.imageWrapper {
  margin: 50px 0 0;
  position: relative;
}

.imageWrapper img {
  width: 500px;
  height: 500px;
}

.controlled-example {
  max-width: 250px;
}

.controlled-example_header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #fff;
}

.controlled-example_close-button {
  cursor: pointer;
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  padding: 0;
}

.controlled-example_close-button:hover {
  color: grey;
}

.internal-scroll-container {
  height: 200px;
  overflow: auto;
}

.internal-scroll-container > div {
  padding-top: 100px;
  height: 400px;
}

.arrow-content-tooltip .react-tooltip-lite {
  box-sizing: border-box;
  border: 1px solid gray;
  border-radius: 8px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.2);
}

.arrow-content-tooltip .react-tooltip-lite-down-arrow svg {
  transform: translateY(1px);
}

.arrow-content-tooltip .react-tooltip-lite-right-arrow svg {
  transform: rotate(270deg) translateY(-4px) translateX(-4px);
}
.arrow-content-tooltip .react-tooltip-lite-up-arrow svg {
  transform: rotate(180deg) translateY(1px);
}
.arrow-content-tooltip .react-tooltip-lite-left-arrow svg {
  transform: rotate(90deg) translateY(5px) translateX(4px);
}

.overlay {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1110;
  backdrop-filter: blur(8px);
}

/* Styles for the modal */
.modal-scrollable {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50%;
  max-height: 80%;
  padding: 20px;
  background-color: #fff;
  z-index: 10;
  overflow-y: auto; /* Makes it scrollable */
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
@media screen and (max-width: 768px) {
  .modal-scrollable {
      width: 90%;
  }
}
.modal-scrollable-header {
  font-size: 24px;
  margin-bottom: 20px;
}
.modal{
  backdrop-filter: blur(8px);
}

.disabled{
  background-color: #d3d3d3;
  opacity: 0.5;
  cursor: not-allowed;
}