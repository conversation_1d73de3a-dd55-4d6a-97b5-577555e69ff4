import React from 'react';

const IconWrapper = ({ children }) => (
    <svg className="w-6 h-6 text-current flex items-center justify-center"
        fill="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg">
        <g transform="translate(6, 6)">
            {children}
        </g>
    </svg>
);

// Mic Icon (Unmuted)
export const MicIcon = () => (
    <svg width="14" height="14" viewBox="0 0 13 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="microphone" clip-path="url(#clip0_3240_6429)">
            <g id="Vector">
                <path d="M10.875 4.78643C10.7092 4.78643 10.5503 4.86018 10.4331 4.99145C10.3158 5.12273 10.25 5.30077 10.25 5.48643V7.58643C10.25 8.32903 9.98661 9.04122 9.51777 9.56633C9.04893 10.0914 8.41304 10.3864 7.75 10.3864H5.25C4.58696 10.3864 3.95107 10.0914 3.48223 9.56633C3.01339 9.04122 2.75 8.32903 2.75 7.58643V5.48643C2.75 5.30077 2.68415 5.12273 2.56694 4.99145C2.44973 4.86018 2.29076 4.78643 2.125 4.78643C1.95924 4.78643 1.80027 4.86018 1.68306 4.99145C1.56585 5.12273 1.5 5.30077 1.5 5.48643V7.58643C1.50099 8.69999 1.8964 9.76763 2.59945 10.555C3.30249 11.3425 4.25574 11.7853 5.25 11.7864H5.875V13.1864H4.625C4.45924 13.1864 4.30027 13.2602 4.18306 13.3914C4.06585 13.5227 4 13.7008 4 13.8864C4 14.0721 4.06585 14.2501 4.18306 14.3814C4.30027 14.5127 4.45924 14.5864 4.625 14.5864H8.375C8.54076 14.5864 8.69973 14.5127 8.81694 14.3814C8.93415 14.2501 9 14.0721 9 13.8864C9 13.7008 8.93415 13.5227 8.81694 13.3914C8.69973 13.2602 8.54076 13.1864 8.375 13.1864H7.125V11.7864H7.75C8.74426 11.7853 9.69751 11.3425 10.4006 10.555C11.1036 9.76763 11.499 8.69999 11.5 7.58643V5.48643C11.5 5.30077 11.4342 5.12273 11.3169 4.99145C11.1997 4.86018 11.0408 4.78643 10.875 4.78643Z" fill="#111928" />
                <path d="M7.125 0.586426H5.875C4.49429 0.586426 3.375 1.84003 3.375 3.38643V6.88643C3.375 8.43282 4.49429 9.68643 5.875 9.68643H7.125C8.50571 9.68643 9.625 8.43282 9.625 6.88643V3.38643C9.625 1.84003 8.50571 0.586426 7.125 0.586426Z" fill="#111928" />
            </g>
        </g>
        <defs>
            <clipPath id="clip0_3240_6429">
                <rect width="12" height="14" fill="white" transform="translate(0.5 0.586426)" />
            </clipPath>
        </defs>
    </svg>
);

// Mic Off Icon (Muted)
export const MicOffIcon = () => (
    <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="mic">
            <path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M15.4846 1.35007C15.7281 1.01496 15.6538 0.545937 15.3187 0.302469C14.9836 0.059002 14.5145 0.133288 14.2711 0.468393L11.7622 3.92149C11.292 3.17267 10.4367 2.67188 9.45948 2.67188H8.11604C6.63212 2.67188 5.42916 3.82671 5.42916 5.25128V8.47553C5.42916 9.44655 5.98806 10.2922 6.81389 10.7323L6.29206 11.4506C6.01709 11.3253 5.76374 11.1549 5.54441 10.9443C5.04052 10.4606 4.75744 9.80448 4.75744 9.12038V7.18583C4.75744 7.01481 4.68667 6.85079 4.5607 6.72985C4.43473 6.60892 4.26387 6.54098 4.08572 6.54098C3.90757 6.54098 3.73672 6.60892 3.61075 6.72985C3.48477 6.85079 3.414 7.01481 3.414 7.18583V9.12038C3.41507 10.1462 3.84003 11.1297 4.59563 11.8551C4.8708 12.1193 5.18162 12.3413 5.51725 12.517L2.51536 16.6487C2.27189 16.9838 2.34618 17.4529 2.68128 17.6963C3.01639 17.9398 3.48541 17.8655 3.72888 17.5304L7.04233 12.9698C7.17532 12.9827 7.30947 12.9894 7.44432 12.9895H8.11604V14.2792H6.7726C6.59445 14.2792 6.42359 14.3471 6.29762 14.4681C6.17165 14.589 6.10088 14.753 6.10088 14.924C6.10088 15.0951 6.17165 15.2591 6.29762 15.38C6.42359 15.501 6.59445 15.5689 6.7726 15.5689H10.8029C10.9811 15.5689 11.1519 15.501 11.2779 15.38C11.4039 15.2591 11.4746 15.0951 11.4746 14.924C11.4746 14.753 11.4039 14.589 11.2779 14.4681C11.1519 14.3471 10.9811 14.2792 10.8029 14.2792H9.45948V12.9895H10.1312C11.1998 12.9885 12.2243 12.5805 12.9799 11.8551C13.7355 11.1297 14.1604 10.1462 14.1615 9.12038V7.18583C14.1615 7.01481 14.0907 6.85079 13.9648 6.72985C13.8388 6.60892 13.6679 6.54098 13.4898 6.54098C13.3116 6.54098 13.1408 6.60892 13.0148 6.72985C12.8888 6.85079 12.8181 7.01481 12.8181 7.18583V9.12038C12.8181 9.80448 12.535 10.4606 12.0311 10.9443C11.5272 11.428 10.8438 11.6998 10.1312 11.6998H7.96507L8.43358 11.0549H9.45948C10.9434 11.0549 12.1464 9.9001 12.1464 8.47553V5.94474L15.4846 1.35007Z" fill="#C81E1E" />
        </g>
    </svg>
);

// Video Icon (Video On)
export const VideoIcon = () => (
    <svg width="14" height="14"viewBox="0 0 13 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_3240_6427)">
            <g>
                <path d="M7.1 2.33643H1.7C1.03726 2.33643 0.5 3.008 0.5 3.83643V11.3364C0.5 12.1649 1.03726 12.8364 1.7 12.8364H7.1C7.76274 12.8364 8.29999 12.1649 8.29999 11.3364V3.83643C8.29999 3.008 7.76274 2.33643 7.1 2.33643Z" fill="#111928" />
                <path d="M12.2 3.31143C12.1091 3.24491 12.0058 3.20948 11.9005 3.20869C11.7952 3.2079 11.6915 3.24178 11.6 3.30693L9.49999 4.79943V10.4492L11.579 12.0992C11.67 12.1712 11.7747 12.2113 11.8824 12.2152C11.99 12.2192 12.0965 12.1869 12.1907 12.1217C12.2848 12.0565 12.3633 11.9608 12.4177 11.8447C12.4721 11.7285 12.5005 11.5963 12.5 11.4617V3.96168C12.5001 3.8299 12.4724 3.70041 12.4198 3.58626C12.3671 3.47211 12.2913 3.37731 12.2 3.31143Z" fill="#111928" />
            </g>
        </g>
        <defs>
            <clipPath id="clip0_3240_6427">
                <rect width="12" height="14" fill="white" transform="translate(0.5 0.586426)" />
            </clipPath>
        </defs>
    </svg>
);

// Video Off Icon
export const VideoOffIcon = () => (
    <svg width="14" height="14" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g>
            <path fillRule="evenodd" clipRule="evenodd" d="M0.46967 15.4697C0.176777 15.7626 0.176777 16.2374 0.46967 16.5303C0.762563 16.8232 1.23744 16.8232 1.53033 16.5303L4.18575 13.8749H9.90001C10.618 13.8749 11.2 13.1554 11.2 12.2678V6.86065L12.5 5.56065V11.3171L14.7523 13.085C14.8508 13.1621 14.9643 13.2051 15.0809 13.2093C15.1975 13.2136 15.3129 13.1789 15.4149 13.1091C15.5169 13.0392 15.6019 12.9367 15.6609 12.8123C15.7198 12.6879 15.7506 12.5462 15.75 12.402V4.36625C15.7501 4.22506 15.7201 4.08632 15.6631 3.96402C15.606 3.84171 15.5239 3.74014 15.425 3.66955C15.3265 3.59829 15.2146 3.56032 15.1005 3.55948C14.9864 3.55863 14.8742 3.59493 14.775 3.66473L13.4991 4.5616L16.5303 1.53033C16.8232 1.23744 16.8232 0.762563 16.5303 0.46967C16.2374 0.176777 15.7626 0.176777 15.4697 0.46967L11.2 4.73933V4.23205C11.2 3.34445 10.618 2.62491 9.90001 2.62491H4.05002C3.33205 2.62491 2.75002 3.34445 2.75002 4.23205V12.2678C2.75002 12.5447 2.80669 12.8054 2.90649 13.0328L0.46967 15.4697Z" fill="#C81E1E" />
        </g>
    </svg>
);

// Screen Share Icon
export const ScreenShareIcon = () => (
    <svg width="14" height="14" className="text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6" />
        <line x1="2" y1="20" x2="2.01" y2="20" />
    </svg>
);

// Users Icon
export const UsersIcon = () => (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_3931_10999)">
            <path d="M4.55 6.63158C6.2897 6.63158 7.7 5.14705 7.7 3.31579C7.7 1.48453 6.2897 0 4.55 0C2.8103 0 1.4 1.48453 1.4 3.31579C1.4 5.14705 2.8103 6.63158 4.55 6.63158Z" fill="black" />
            <path d="M5.6 7.36842H3.5C2.57208 7.36959 1.68249 7.75812 1.02635 8.44879C0.370217 9.13946 0.0011115 10.0759 0 11.0526L0 13.2632C0 13.4586 0.0737498 13.646 0.205025 13.7842C0.336301 13.9224 0.514348 14 0.7 14H8.4C8.58565 14 8.7637 13.9224 8.89497 13.7842C9.02625 13.646 9.1 13.4586 9.1 13.2632V11.0526C9.09889 10.0759 8.72978 9.13946 8.07365 8.44879C7.41751 7.75812 6.52792 7.36959 5.6 7.36842Z" fill="black" />
            <path d="M7.1183 5.22273C7.49584 4.66621 7.69918 3.99964 7.7 3.31579C7.6985 3.07299 7.67174 2.83109 7.6202 2.59442C7.33512 2.9538 7.13988 3.38248 7.05223 3.8415C6.96458 4.30051 6.98729 4.77531 7.1183 5.22273Z" fill="black" />
            <path d="M9.8 1.47368C9.46472 1.47638 9.13269 1.54304 8.82 1.67042C9.10793 2.48619 9.17709 3.36961 9.01992 4.22399C8.86275 5.07837 8.4853 5.87076 7.9289 6.51442C8.24288 6.69763 8.53698 6.91629 8.806 7.16653C9.12275 7.2975 9.4597 7.36594 9.8 7.36842C10.5426 7.36842 11.2548 7.0579 11.7799 6.50516C12.305 5.95242 12.6 5.20274 12.6 4.42105C12.6 3.63936 12.305 2.88969 11.7799 2.33695C11.2548 1.78421 10.5426 1.47368 9.8 1.47368Z" fill="black" />
            <path d="M10.5 8.10526H9.6061C10.186 8.96699 10.4983 9.99658 10.5 11.0526V13.2632C10.4977 13.5148 10.4542 13.764 10.3712 14H13.3C13.4857 14 13.6637 13.9224 13.795 13.7842C13.9263 13.646 14 13.4586 14 13.2632V11.7895C13.9989 10.8127 13.6298 9.87631 12.9736 9.18564C12.3175 8.49497 11.4279 8.10643 10.5 8.10526Z" fill="black" />
        </g>
        <defs>
            <clipPath id="clip0_3931_10999">
                <rect width="14" height="14" fill="white" />
            </clipPath>
        </defs>
    </svg>
);

// Chat Icon
export const ChatIcon = () => (
    <svg width="14" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_3775_3854)">
            <path d="M10.8 3.43486H9.6V6.99967C9.6 7.75603 9.34714 8.48141 8.89706 9.01624C8.44697 9.55106 7.83652 9.85153 7.2 9.85153H5.4L4.1076 11.003C4.30796 11.1796 4.55051 11.2758 4.8 11.2775H7.0002L9.24 13.2737C9.34386 13.3663 9.47018 13.4163 9.6 13.4163C9.75913 13.4163 9.91174 13.3412 10.0243 13.2075C10.1368 13.0738 10.2 12.8925 10.2 12.7034V11.2775H10.8C11.1183 11.2775 11.4235 11.1272 11.6485 10.8598C11.8736 10.5924 12 10.2297 12 9.85153V4.86079C12 4.48261 11.8736 4.11992 11.6485 3.8525C11.4235 3.58509 11.1183 3.43486 10.8 3.43486Z" fill="#111928" />
            <path d="M7.2 0.583008H1.2C0.88174 0.583008 0.576515 0.733239 0.351472 1.00065C0.126428 1.26806 0 1.63075 0 2.00893L0 6.99967C0 7.37785 0.126428 7.74054 0.351472 8.00796C0.576515 8.27537 0.88174 8.4256 1.2 8.4256H1.8V9.85153C1.8 9.98393 1.83103 10.1137 1.88961 10.2264C1.94819 10.339 2.03201 10.43 2.13167 10.4892C2.23134 10.5484 2.34291 10.5735 2.45388 10.5616C2.56486 10.5497 2.67086 10.5013 2.76 10.4219L4.9998 8.4256H7.2C7.51826 8.4256 7.82348 8.27537 8.04853 8.00796C8.27357 7.74054 8.4 7.37785 8.4 6.99967V2.00893C8.4 1.63075 8.27357 1.26806 8.04853 1.00065C7.82348 0.733239 7.51826 0.583008 7.2 0.583008Z" fill="#111928" />
        </g>
        <defs>
            <clipPath id="clip0_3775_3854">
                <rect width="12" height="14" fill="white" />
            </clipPath>
        </defs>
    </svg>
);

// End Call Icon
export const EndCallIcon = () => (
    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_4189_17708)">
<path d="M5.73125 8.35201C6.01019 8.63003 6.38798 8.78615 6.78184 8.78615C7.17569 8.78615 7.55348 8.63003 7.83242 8.35201L8.35771 7.8268C8.63664 7.54878 9.01443 7.39266 9.40829 7.39266C9.80214 7.39266 10.1799 7.54878 10.4589 7.8268L11.5095 8.87722C11.663 9.00581 11.7866 9.16651 11.8713 9.34799C11.9561 9.52948 12 9.72734 12 9.92764C12 10.1279 11.9561 10.3258 11.8713 10.5073C11.7866 10.6888 11.663 10.8495 11.5095 10.9781C8.75843 13.7294 5.59168 12.4224 2.8324 9.66353C0.0731247 6.90468 -1.22734 3.74141 1.52443 0.990057C1.65311 0.836605 1.81386 0.713206 1.99536 0.628541C2.17687 0.543876 2.37472 0.5 2.57501 0.5C2.77529 0.5 2.97315 0.543876 3.15465 0.628541C3.33616 0.713206 3.49691 0.836605 3.62559 0.990057L4.67617 2.04048C4.95442 2.31927 5.11068 2.69704 5.11068 3.0909C5.11068 3.48476 4.95442 3.86253 4.67617 4.14132L4.15088 4.66653C3.87263 4.94532 3.71637 5.32309 3.71637 5.71695C3.71637 6.11081 3.87263 6.48858 4.15088 6.76737L5.73125 8.35201Z" fill="#E02424"/>
</g>
<defs>
<clipPath id="clip0_4189_17708">
<rect width="12" height="12" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

);

// stop screen share Icon
export const StopScreenShareIcon = () => (
<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.33333 8.22913H0.777778C0.571498 8.22913 0.373667 8.1523 0.227806 8.01556C0.0819442 7.87881 0 7.69335 0 7.49996C0 7.30657 0.0819442 7.12111 0.227806 6.98436C0.373667 6.84762 0.571498 6.77079 0.777778 6.77079H9.33333C9.53961 6.77079 9.73744 6.84762 9.88331 6.98436C10.0292 7.12111 10.1111 7.30657 10.1111 7.49996C10.1111 7.69335 10.0292 7.87881 9.88331 8.01556C9.73744 8.1523 9.53961 8.22913 9.33333 8.22913Z" fill="#6B7280"/>
<path d="M6.22222 11.1458C6.06842 11.1458 5.91807 11.103 5.7902 11.0229C5.66232 10.9427 5.56266 10.8289 5.50381 10.6956C5.44495 10.5624 5.42955 10.4158 5.45955 10.2744C5.48954 10.133 5.56359 10.0031 5.67233 9.9011L8.23356 7.49996L5.67233 5.09881C5.59805 5.03155 5.53879 4.95109 5.49803 4.86213C5.45727 4.77317 5.43581 4.67749 5.43492 4.58067C5.43402 4.48385 5.4537 4.38783 5.49281 4.29822C5.53191 4.20861 5.58966 4.1272 5.66269 4.05873C5.73572 3.99027 5.82256 3.93613 5.91815 3.89946C6.01373 3.8628 6.11615 3.84435 6.21942 3.84519C6.3227 3.84603 6.42476 3.86615 6.51965 3.90436C6.61454 3.94258 6.70036 3.99813 6.77211 4.06777L9.88331 6.98436C10.0292 7.12111 10.1111 7.30657 10.1111 7.49996C10.1111 7.69335 10.0292 7.87881 9.88331 8.01556L6.77211 10.9321C6.62628 11.0689 6.42848 11.1458 6.22222 11.1458Z" fill="#6B7280"/>
<path d="M11.6667 13.3333H9.33333C9.12705 13.3333 8.92922 13.2565 8.78336 13.1197C8.6375 12.983 8.55556 12.7975 8.55556 12.6041C8.55556 12.4107 8.6375 12.2253 8.78336 12.0885C8.92922 11.9518 9.12705 11.875 9.33333 11.875H11.6667C11.8729 11.875 12.0708 11.7981 12.2166 11.6614C12.3625 11.5246 12.4444 11.3392 12.4444 11.1458V3.85413C12.4444 3.66074 12.3625 3.47527 12.2166 3.33853C12.0708 3.20178 11.8729 3.12496 11.6667 3.12496H9.33333C9.12705 3.12496 8.92922 3.04814 8.78336 2.91139C8.6375 2.77465 8.55556 2.58918 8.55556 2.39579C8.55556 2.20241 8.6375 2.01694 8.78336 1.88019C8.92922 1.74345 9.12705 1.66663 9.33333 1.66663H11.6667C12.2855 1.66663 12.879 1.89709 13.3166 2.30733C13.7542 2.71757 14 3.27396 14 3.85413V11.1458C14 11.726 13.7542 12.2824 13.3166 12.6926C12.879 13.1028 12.2855 13.3333 11.6667 13.3333Z" fill="#6B7280"/>
</svg>

);

// Eye Icon
export const EyeIcon = () => (
    <svg width="14" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6 1.75C2.7672 1.75 0 5.752 0 7C0 8.3065 2.1276 12.25 6 12.25C9.8724 12.25 12 8.3065 12 7C12 5.752 9.2328 1.75 6 1.75ZM6 9.25C5.64399 9.25 5.29598 9.11804 4.99997 8.87081C4.70397 8.62357 4.47325 8.27217 4.33702 7.86104C4.20078 7.4499 4.16513 6.9975 4.23459 6.56105C4.30404 6.12459 4.47547 5.72368 4.72721 5.40901C4.97894 5.09434 5.29967 4.88005 5.64884 4.79323C5.998 4.70642 6.35992 4.75097 6.68883 4.92127C7.01774 5.09157 7.29886 5.37996 7.49665 5.74997C7.69443 6.11998 7.8 6.55499 7.8 7C7.8 7.59674 7.61036 8.16903 7.27279 8.59099C6.93523 9.01295 6.47739 9.25 6 9.25Z" fill="#111928" />
    </svg>
);

// Eye Off Icon
export const EyeOffIcon = () => (
    <svg width="14" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_3777_4353)">
            <path d="M1.2 9.55359L3.033 7.37845C3.01621 7.253 3.0052 7.12655 3 6.99967C3.00095 6.05586 3.31733 5.15103 3.87973 4.48366C4.44213 3.81628 5.20464 3.44085 6 3.43972C6.10691 3.44565 6.21347 3.45849 6.3192 3.47817L7.3656 2.23646C6.92025 2.09376 6.46129 2.01957 6 2.01574C2.7672 2.01574 0 5.81492 0 6.99967C0.156483 7.98268 0.577575 8.87887 1.2 9.55359Z" fill="black" />
            <path d="M7.62 6.08405L11.2242 1.80713C11.2815 1.74145 11.3272 1.66288 11.3587 1.57602C11.3901 1.48915 11.4067 1.39572 11.4074 1.30119C11.408 1.20665 11.3929 1.11289 11.3627 1.02539C11.3325 0.937891 11.288 0.858396 11.2316 0.791545C11.1753 0.724694 11.1083 0.671827 11.0346 0.636027C10.9608 0.600228 10.8818 0.582213 10.8022 0.583035C10.7225 0.583856 10.6438 0.603497 10.5706 0.640812C10.4974 0.678127 10.4311 0.732368 10.3758 0.80037L6.7716 5.0773C6.65874 5.01294 6.54049 4.96282 6.4188 4.92778L6.3978 4.91995C6.1013 4.83725 5.7919 4.84577 5.49895 4.94471C5.206 5.04364 4.93919 5.22972 4.72382 5.48529C4.50844 5.74087 4.35164 6.05747 4.26826 6.40511C4.18489 6.75274 4.17771 7.11988 4.2474 7.47172C4.2474 7.48027 4.2522 7.48739 4.2534 7.49522C4.28294 7.64017 4.32538 7.78099 4.38 7.91529L0.7758 12.1922C0.718494 12.2579 0.672785 12.3365 0.641339 12.4233C0.609894 12.5102 0.593342 12.6036 0.59265 12.6982C0.591958 12.7927 0.607139 12.8865 0.637307 12.974C0.667476 13.0615 0.712028 13.141 0.768363 13.2078C0.824699 13.2747 0.89169 13.3275 0.965428 13.3633C1.03917 13.3991 1.11817 13.4171 1.19784 13.4163C1.27751 13.4155 1.35624 13.3959 1.42944 13.3585C1.50265 13.3212 1.56885 13.267 1.6242 13.199L5.2284 8.92205C5.34158 8.98687 5.46025 9.03723 5.5824 9.07228C5.589 9.07228 5.595 9.07726 5.6022 9.0794C5.89871 9.1621 6.2081 9.15358 6.50105 9.05464C6.794 8.9557 7.06081 8.76963 7.27618 8.51406C7.49156 8.25848 7.64836 7.94187 7.73174 7.59424C7.81511 7.24661 7.82229 6.87947 7.7526 6.52762C7.7526 6.51908 7.7478 6.51125 7.746 6.5027C7.71647 6.3583 7.67424 6.21798 7.62 6.08405Z" fill="black" />
            <path d="M10.6926 4.57392L8.9784 6.60808C9.03287 7.13914 8.98478 7.67795 8.83777 8.1837C8.69077 8.68944 8.44871 9.14886 8.12992 9.52715C7.81113 9.90544 7.42398 10.1927 6.99779 10.3671C6.57159 10.5416 6.11753 10.5986 5.67 10.534L4.599 11.8049C5.05943 11.9226 5.52901 11.9825 6 11.9836C9.8718 11.9836 12 8.23996 12 6.99967C12 6.16949 11.0178 4.95341 10.6926 4.57392Z" fill="black" />
        </g>
        <defs>
            <clipPath id="clip0_3777_4353">
                <rect width="12" height="14" fill="white" />
            </clipPath>
        </defs>
    </svg>
);

// Fullscreen Icon
export const FullScreenIcon = () => (
    <svg width="14" height="14" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 2.66667V5.33333C2 5.702 2.29867 6 2.66667 6C3.03467 6 3.33333 5.702 3.33333 5.33333V4.12733L5.69067 6.47133C5.95133 6.732 6.37267 6.732 6.63333 6.47133C6.76333 6.34133 6.82867 6.17067 6.82867 6C6.82867 5.82933 6.76333 5.65867 6.63333 5.52867L4.426 3.33333H5.33333C5.70133 3.33333 6 3.03533 6 2.66667C6 2.298 5.70133 2 5.33333 2H2.66667C2.29867 2 2 2.298 2 2.66667Z" fill="#111928" />
        <path d="M10 2.66667C10 3.03533 10.2987 3.33333 10.6667 3.33333L11.7313 3.33333L9.52867 5.52867C9.268 5.78933 9.268 6.21067 9.52867 6.47133C9.78933 6.732 10.2107 6.732 10.4713 6.47133L12.6667 4.28333L12.6667 5.33333C12.6667 5.702 12.9653 6 13.3333 6C13.7013 6 14 5.702 14 5.33333L14 2.66667C14 2.298 13.7013 2 13.3333 2L10.6667 2C10.2987 2 10 2.298 10 2.66667Z" fill="#111928" />
        <path d="M3.33333 11.724V10.6667C3.33333 10.298 3.03467 10 2.66667 10C2.29867 10 2 10.298 2 10.6667L2 13.3333C2 13.42 2.018 13.5067 2.05133 13.588C2.11867 13.7513 2.24867 13.8813 2.412 13.9487C2.49333 13.982 2.58 14 2.66667 14H5.33333C5.70133 14 6 13.702 6 13.3333C6 12.9647 5.70133 12.6667 5.33333 12.6667H4.276L6.63333 10.31C6.76333 10.18 6.82867 10.0093 6.82867 9.83867C6.82867 9.668 6.76333 9.49733 6.63333 9.36733C6.37267 9.10667 5.95133 9.10667 5.69067 9.36733L3.33333 11.724Z" fill="#111928" />
        <path d="M9.52867 10.31L11.8853 12.6667H10.6667C10.2987 12.6667 10 12.9647 10 13.3333C10 13.702 10.2987 14 10.6667 14L13.3333 14C13.7013 14 14 13.702 14 13.3333V10.6667C14 10.298 13.7013 10 13.3333 10C12.9653 10 12.6667 10.298 12.6667 10.6667V11.5627L10.4713 9.36733C10.2107 9.10667 9.78933 9.10667 9.52867 9.36733C9.268 9.628 9.268 10.0493 9.52867 10.31Z" fill="#111928" />
    </svg>
);

// Exit Fullscreen Icon
export const ExitFullScreenIcon = () => (
    <svg width="14" height="14" className=" text-white" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" fill="none" strokeLinecap="round" strokeLinejoin="round">
        <path stroke="none" d="M0 0h24v24H0z" />
        <path d="M15 19v-2a2 2 0 0 1 2 -2h2" />
        <path d="M15 5v2a2 2 0 0 0 2 2h2" />
        <path d="M5 15h2a2 2 0 0 1 2 2v2" />
        <path d="M5 9h2a2 2 0 0 0 2 -2v-2" />
    </svg>
);

export const InviteIcon = () => {
    return (
        <svg width="14" height="14" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_3931_18493)">
                <path d="M4.4 6.18421C5.89117 6.18421 7.1 4.91176 7.1 3.34211C7.1 1.77245 5.89117 0.5 4.4 0.5C2.90883 0.5 1.7 1.77245 1.7 3.34211C1.7 4.91176 2.90883 6.18421 4.4 6.18421Z" fill="black" />
                <path d="M5.3 6.81579H3.5C2.70464 6.81679 1.94213 7.14982 1.37973 7.74182C0.817329 8.33383 0.500953 9.13647 0.5 9.97368V11.8684C0.5 12.0359 0.563214 12.1966 0.675736 12.315C0.788258 12.4335 0.94087 12.5 1.1 12.5H7.7C7.85913 12.5 8.01174 12.4335 8.12426 12.315C8.23679 12.1966 8.3 12.0359 8.3 11.8684V9.97368C8.29905 9.13647 7.98267 8.33383 7.42027 7.74182C6.85787 7.14982 6.09536 6.81679 5.3 6.81579Z" fill="black" />
                <path d="M11.9 4.92105H10.7V3.65789C10.7 3.49039 10.6368 3.32975 10.5243 3.2113C10.4117 3.09286 10.2591 3.02632 10.1 3.02632C9.94087 3.02632 9.78826 3.09286 9.67574 3.2113C9.56321 3.32975 9.5 3.49039 9.5 3.65789V4.92105H8.3C8.14087 4.92105 7.98826 4.98759 7.87574 5.10604C7.76321 5.22448 7.7 5.38513 7.7 5.55263C7.7 5.72014 7.76321 5.88078 7.87574 5.99923C7.98826 6.11767 8.14087 6.18421 8.3 6.18421H9.5V7.44737C9.5 7.61487 9.56321 7.77552 9.67574 7.89396C9.78826 8.01241 9.94087 8.07895 10.1 8.07895C10.2591 8.07895 10.4117 8.01241 10.5243 7.89396C10.6368 7.77552 10.7 7.61487 10.7 7.44737V6.18421H11.9C12.0591 6.18421 12.2117 6.11767 12.3243 5.99923C12.4368 5.88078 12.5 5.72014 12.5 5.55263C12.5 5.38513 12.4368 5.22448 12.3243 5.10604C12.2117 4.98759 12.0591 4.92105 11.9 4.92105Z" fill="black" />
            </g>
            <defs>
                <clipPath id="clip0_3931_18493">
                    <rect width="12" height="12" fill="white" transform="translate(0.5 0.5)" />
                </clipPath>
            </defs>
        </svg>
    );
};

export const MultipleTabs = () => (
    <svg width="14" height="14" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_3931_19408)">
            <path d="M4.5 4C3.67157 4 3 4.67157 3 5.5V12.5C3 13.3284 3.67157 14 4.5 14H11.5C12.3284 14 13 13.3284 13 12.5V5.5C13 4.67157 12.3284 4 11.5 4H4.5ZM4.5 5H11.5C11.7761 5 12 5.22386 12 5.5V12.5C12 12.7761 11.7761 13 11.5 13H4.5C4.22386 13 4 12.7761 4 12.5V5.5C4 5.22386 4.22386 5 4.5 5Z" fill="black" />
            <path d="M8.7 1.5C8.03726 1.5 7.5 2.03726 7.5 2.7V8.3C7.5 8.96274 8.03726 9.5 8.7 9.5H14.3C14.9627 9.5 15.5 8.96274 15.5 8.3V2.7C15.5 2.03726 14.9627 1.5 14.3 1.5H8.7ZM8.7 2.3H14.3C14.5209 2.3 14.7 2.47909 14.7 2.7V8.3C14.7 8.52091 14.5209 8.7 14.3 8.7H8.7C8.47909 8.7 8.3 8.52091 8.3 8.3V2.7C8.3 2.47909 8.47909 2.3 8.7 2.3Z" fill="black" />
            <path d="M7.5 5C7.08579 5 6.5 5.08579 6.5 5.5L7.41016 9.25C7.41016 9.66422 7.74594 10 8.16016 10H11.6602C12.0744 10 12 9.91421 12 9.5V5.8611C12 5.44689 12.0744 5 11.6602 5H7.5ZM8.41016 5.5H11.6602C11.7982 5.5 11.9102 5.61193 11.9102 5.75L11.5 8.5C11.5 8.63807 11.1381 8.5 11 8.5H8.5C8.36193 8.5 8.5 8.63807 8.5 8.5L8.41016 6C8.41016 5.86193 8.27209 5.5 8.41016 5.5Z" fill="white" />
        </g>
        <defs>
            <clipPath id="clip0_3931_19408">
                <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
            </clipPath>
        </defs>
    </svg>
)

export const ActiveChat = () => (
    <svg width="14" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10.7998 3.93457C10.9355 3.93457 11.083 3.99041 11.2119 4.11621L11.2656 4.1748C11.4083 4.34435 11.4999 4.58976 11.5 4.86035V9.85156C11.5 10.0884 11.4304 10.3063 11.3174 10.4707L11.2656 10.5381C11.1251 10.7049 10.9548 10.7773 10.7998 10.7773H9.7002V12.7031C9.7002 12.7439 9.69337 12.7807 9.68262 12.8115L9.6416 12.8857C9.6274 12.9026 9.61491 12.9099 9.6084 12.9131C9.60557 12.9145 9.60377 12.9157 9.60254 12.916C9.6014 12.9163 9.59992 12.916 9.59961 12.916C9.59892 12.916 9.59735 12.916 9.59473 12.915L9.57227 12.9004L7.33301 10.9043L7.19043 10.7773H5.1123L5.58984 10.3516H7.2002C7.94983 10.3515 8.65154 10.019 9.17676 9.4541L9.2793 9.33789C9.8116 8.70537 10.0995 7.86364 10.0996 7V3.93457H10.7998ZM1.2002 1.08301H7.2002C7.33596 1.08306 7.48338 1.13867 7.6123 1.26465L7.66602 1.32227C7.80875 1.49187 7.90036 1.73807 7.90039 2.00879V7C7.90033 7.23693 7.82995 7.45477 7.7168 7.61914L7.66602 7.68555C7.52548 7.85254 7.35522 7.92572 7.2002 7.92578H4.80957L4.66699 8.05273L2.42773 10.0488C2.41905 10.0566 2.41143 10.0606 2.40723 10.0625C2.40333 10.0642 2.40092 10.0644 2.40039 10.0645H2.39844C2.397 10.0641 2.3926 10.0631 2.38672 10.0596C2.38071 10.056 2.37264 10.0492 2.36328 10.0391L2.33301 9.99609C2.3131 9.95783 2.29981 9.9072 2.2998 9.85156V7.92578H1.2002C1.06448 7.92578 0.917 7.86998 0.788086 7.74414L0.734375 7.68555C0.591725 7.51598 0.500073 7.2706 0.5 7V2.00879L0.503906 1.9082C0.520606 1.71062 0.585795 1.53045 0.682617 1.38965L0.734375 1.32227C0.839632 1.19733 0.961388 1.12495 1.08105 1.09668L1.2002 1.08301Z" stroke="#1C64F2" />
    </svg>

)

export const ActiveMember = () => (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5.59961 7.86816C6.38645 7.86922 7.14629 8.19871 7.71094 8.79297C8.27638 9.38817 8.59852 10.2005 8.59961 11.0527V13.2627C8.59961 13.3335 8.57274 13.3968 8.53223 13.4395C8.49263 13.4811 8.44481 13.4999 8.40039 13.5H0.700195C0.655668 13.5 0.607101 13.4813 0.567383 13.4395C0.526974 13.3968 0.5 13.3334 0.5 13.2627V11.0527L0.515625 10.7354C0.580787 10.0532 0.851542 9.41233 1.28613 8.90723L1.38867 8.79297C1.91813 8.23565 2.61943 7.91122 3.35352 7.87207L3.50098 7.86816H5.59961ZM9.80176 1.97363C10.3273 1.97406 10.8384 2.16622 11.248 2.51953L11.417 2.68164C11.8513 3.13884 12.0996 3.76383 12.0996 4.4209C12.0996 5.07802 11.8514 5.7039 11.417 6.16113C10.9837 6.61705 10.4018 6.86697 9.80078 6.86719C9.54944 6.86501 9.30094 6.8169 9.06348 6.72754C8.93072 6.60891 8.79338 6.49632 8.65039 6.3916C9.0103 5.87003 9.27845 5.28573 9.43555 4.66406L9.51172 4.31445C9.65273 3.54792 9.62531 2.76074 9.43945 2.00879C9.55907 1.98709 9.68013 1.97474 9.80176 1.97363ZM4.5498 0.5C5.72452 0.5 6.74562 1.32333 7.08203 2.48438C6.82564 2.86299 6.64831 3.29357 6.56152 3.74805C6.47442 4.20436 6.48105 4.67367 6.5791 5.12598C6.08901 5.74388 5.35555 6.13184 4.5498 6.13184C3.11019 6.13172 1.90039 4.89495 1.90039 3.31543C1.90057 1.73608 3.1103 0.500112 4.5498 0.5ZM11 11.0518C10.9986 10.2011 10.8126 9.36507 10.46 8.60547H10.499L10.6465 8.60938C11.3806 8.64853 12.0819 8.97296 12.6113 9.53027C13.1061 10.0511 13.4143 10.7381 13.4844 11.4727L13.5 11.79V13.2627C13.5 13.3334 13.473 13.3968 13.4326 13.4395C13.3929 13.4813 13.3443 13.5 13.2998 13.5H10.9883V13.4971L11 13.2676V11.0518Z" fill="white" stroke="#1C64F2" />
    </svg>
)

export const ActiveDropdown = () => (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6.99961 2.8C7.77281 2.8 8.39961 2.1732 8.39961 1.4C8.39961 0.626801 7.77281 0 6.99961 0C6.22641 0 5.59961 0.626801 5.59961 1.4C5.59961 2.1732 6.22641 2.8 6.99961 2.8Z" fill="#1C64F2" />
        <path d="M6.99961 8.4C7.77281 8.4 8.39961 7.7732 8.39961 7C8.39961 6.2268 7.77281 5.6 6.99961 5.6C6.22641 5.6 5.59961 6.2268 5.59961 7C5.59961 7.7732 6.22641 8.4 6.99961 8.4Z" fill="#1C64F2" />
        <path d="M6.99961 14C7.77281 14 8.39961 13.3732 8.39961 12.6C8.39961 11.8268 7.77281 11.2 6.99961 11.2C6.22641 11.2 5.59961 11.8268 5.59961 12.6C5.59961 13.3732 6.22641 14 6.99961 14Z" fill="#1C64F2" />
    </svg>

)