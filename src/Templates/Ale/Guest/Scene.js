import React, { Component } from "react";
import * as Sessions from '../../../Actions/Sessions'
import { connect } from "react-redux";
import Fire from "../../../config/Firebase";
import LoaderOverlay from "../../../components/LoaderOverlay";

class AleGuest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ale_link: "",
      loading: true,
      error: null,
    }
  }
  componentDidMount() {
    console.log("ALE Guest - Component mounted, roomId:", this.props.roomId);

    // Set up custom message subscription
    this.props.SubscribeToCustom((msg) => {
      try {
        var data = JSON.parse(msg);
        var contentWindow = document.getElementById('showcase_frame')?.contentWindow;
        if (contentWindow) {
          contentWindow.postMessage({ ...data, simulate: true }, "*");
        }
      } catch (error) {
        console.error("ALE Guest - Error processing custom message:", error);
      }
    });

    // Set a timeout to prevent infinite loading
    this.loadingTimeout = setTimeout(() => {
      if (this.state.loading) {
        console.error("ALE Guest - Loading timeout reached");
        this.setState({
          error: "Loading timeout: The host may not have started the experience yet. Please wait for the host to share the project.",
          loading: false,
        });
      }
    }, 30000); // 30 second timeout for guest

    // Set up Firebase listener for config changes
    this.setupConfigListener();
  }

  componentWillUnmount() {
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }
    if (this.configUnsubscribe) {
      this.configUnsubscribe();
    }
  }

  setupConfigListener = () => {
    console.log("ALE Guest - Setting up config listener...");

    this.configUnsubscribe = Fire.firestore()
      .collection('sessions')
      .doc(this.props.roomId)
      .collection("config")
      .doc("data")
      .onSnapshot(
        { includeMetadataChanges: true },
        (doc) => {
          console.log("ALE Guest - Config document updated:", doc.exists, doc.data());

          if (!doc.exists) {
            console.log("ALE Guest - Config document does not exist yet, waiting...");
            return;
          }

          const config_data = doc.data();

          // Check if host is switching projects
          if (config_data && config_data.projectSwitching) {
            console.log("ALE Guest - Host is switching projects, showing loader...");
            this.setState({
              ale_link: "",
              loading: true,
              error: null
            });

            // Reset timeout for project switch
            if (this.loadingTimeout) {
              clearTimeout(this.loadingTimeout);
            }
            this.loadingTimeout = setTimeout(() => {
              if (this.state.loading) {
                console.error("ALE Guest - Project switch timeout reached");
                this.setState({
                  error: "Project switch timeout: Please wait for the host to complete the project switch.",
                  loading: false,
                });
              }
            }, 30000);

            return;
          }

          if (config_data && config_data.ale_link) {
            console.log("ALE Guest - Found ALE link:", config_data.ale_link);

            // Add guest parameter to the URL
            let ale_link_with_guest_param;
            if (config_data.ale_link.includes("?")) {
              ale_link_with_guest_param = config_data.ale_link + "&salestoolGuest=true";
            } else {
              ale_link_with_guest_param = config_data.ale_link + "?salestoolGuest=true";
            }

            console.log("ALE Guest - Final guest URL:", ale_link_with_guest_param);

            this.setState({
              ale_link: ale_link_with_guest_param,
              loading: false,
              error: null
            });

            // Clear the timeout since we successfully loaded
            if (this.loadingTimeout) {
              clearTimeout(this.loadingTimeout);
              this.loadingTimeout = null;
            }
          } else if (config_data && config_data.ale_link === null) {
            // Host explicitly cleared the link (project switching)
            console.log("ALE Guest - Host cleared ALE link, waiting for new project...");
            this.setState({
              ale_link: "",
              loading: true,
              error: null
            });
          } else {
            console.log("ALE Guest - Config data exists but no ale_link found:", config_data);
            // Don't set error immediately, keep waiting for host to set the link
          }
        },
        (error) => {
          console.error("ALE Guest - Firebase listener error:", error);
          this.setState({
            error: "Failed to connect to session: " + error.message,
            loading: false,
          });
        }
      );
  }


  render() {
    const { ale_link, loading, error } = this.state;

    console.log("ALE Guest - Rendering state:", { ale_link: !!ale_link, loading, error });

    if (loading) {
      // Check if we had a previous link (indicating project switch)
      const isProjectSwitch = this.previousAleLink && !ale_link;

      return (
        <LoaderOverlay
          title={isProjectSwitch ? "Switching Project" : "Preparing Your Virtual Experience"}
          message={isProjectSwitch ?
            "The host is switching to a new project. Please wait..." :
            "Waiting for the host to start the experience..."
          }
        />
      );
    }

    if (error) {
      return (
        <LoaderOverlay
          title="Unable to Load Experience"
          message={error}
        />
      );
    }

    // Store the current link for project switch detection
    if (ale_link && ale_link !== this.previousAleLink) {
      this.previousAleLink = ale_link;
    }

    return (
      <iframe
        id="showcase_frame"
        title="ALE Virtual Experience"
        src={ale_link}
        style={{ width: "100%", height: "100%", position: "absolute" }}
        onLoad={() => console.log("ALE Guest - Iframe loaded successfully")}
        onError={() => console.error("ALE Guest - Iframe failed to load")}
      />
    );
  }
}
const mapStateToProps = state => {
  return {
    configDetails: state.Sessions.configData,
  }
}
const mapDispatchTothisprops = {
  ...Sessions
}
export default connect(mapStateToProps, mapDispatchTothisprops)(AleGuest);