import React from "react"
import Firebase from "../../../config/Firebase"
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction";
import { PostRequestWithHeaders } from "../../../Tools/helpers/api";
import { UpdateSessionApi } from "../../../Tools/helpers/BackendApis";
import PixelStreaming from "../../PixelStreaming/Guest/Pixel"
import Lark from "../../LarkXr/Guest/lark"
import AleGuest from "../../Ale/Guest/Scene"
import LoaderOverlay from "../../../components/LoaderOverlay";
import VideoCard from "./VideoCard";


class TypeSwitch extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            currentTime: new Date(),
            interval: 30,
            isSessionStarted: false,
            isSessionEnded: false,
            isNearingEnd: false,

        }
        this.SetDuration = this.SetDuration.bind(this);
        this.intervalId = null;
        this.job = null;

    }

    componentDidMount() {
        // console.log("componentDidMount",this.props.Config.end_time,this.props.Config)
        // Initialize component state and set up listeners --mayank
        if (!this.props.Config) {
            this.fetchConfigData();
        } else {
            this.initializeSessionState(this.props.Config);
        }

        this.setupIntervals();
        this.setupSessionListener();
    }

    componentDidUpdate(prevProps) {
        // Handle stream updates for screen sharing
        if (prevProps.Peers !== this.props.Peers || prevProps.userVideoAudio !== this.props.userVideoAudio) {
            // Find sharing peer
            const sharingPeer = this.props.Peers && Object.values(this.props.Peers).find(peer =>
                peer && this.props.userVideoAudio && this.props.userVideoAudio[peer.peerID] &&
                this.props.userVideoAudio[peer.peerID].screen
            );

            if (sharingPeer) {
                // Try to attach stream to video element
                const videoElement = document.getElementById(sharingPeer.peerID + "_SCREEN_VIDEO");
                if (videoElement && sharingPeer.streams && sharingPeer.streams[0] && !videoElement.srcObject) {
                    console.log('Guest TypeSwitch - componentDidUpdate: Setting video srcObject:', sharingPeer.streams[0]);
                    videoElement.srcObject = sharingPeer.streams[0];
                }
            }
        }
    }

    componentWillUnmount() {
        // Clean up intervals to prevent memory leaks --mayank
        if (this.job) clearInterval(this.job);
        if (this.intervalId) clearInterval(this.intervalId);
    }

    fetchConfigData = () => {
        // Fetch session configuration data from Firestore --mayank
        Firebase.firestore().collection('sessions').doc(this.props.roomId).get()
            .then((doc) => {
                if (doc.exists) {
                    const configData = doc.data();
                    this.props.SetConfig(configData);
                    this.initializeSessionState(configData);
                } else {
                    console.log("No such document!");
                }
            })
            .catch((error) => {
                console.log("Error getting document:", error);
            });
    }

    setupSessionListener = () => {
        // Set up real-time listener for session updates --mayank
        Firebase.firestore().collection('sessions').doc(this.props.roomId).onSnapshot({includeMetadataChanges: true}, (doc) => {
            if (!doc.exists) {
                window.location = `/salestool/feedback/${this.props.roomId}`;
                return;
            }
            if (doc.data().status === "ended") {
                window.location = `/salestool/feedback/${this.props.roomId}/${this.props.ClientData.GuestId}`;
                return;
            }
            const configData = doc.data();
            this.props.SetConfig(configData);
            console.log("Setting config", configData);
            this.initializeSessionState(configData);
        });
    }

    initializeSessionState = (config) => {
        // Set initial session state based on config data --mayank
        const isSessionStarted = this.CheckStartTiming(config.schedule_time, config.end_time);
        const isSessionEnded = this.checkEndTimimg(config.schedule_time, config.end_time);
        this.setState({
            isSessionStarted: isSessionStarted,
            isSessionEnded: isSessionEnded
        });
    }
    checkNearingEnd = (endTimestamp) => {
        const endTime = new Date(endTimestamp);
        const currentTime = new Date();
        const timeDifference = endTime.getTime() - currentTime.getTime();
        const fiveMinutesInMs = 5 * 60 * 1000;
        return timeDifference <= fiveMinutesInMs && timeDifference > 0;
    }
    setupIntervals = () => {
        // Set up intervals for updating duration and checking session state --mayank
        this.job = setInterval(() => {
            this.SetDuration(this.state.interval);
            this.props.SetLastUpdate((new Date().getTime()));
        }, this.state.interval * 1000);

        this.intervalId = setInterval(() => {
            if (this.props.Config) {
                const isSessionStarted = this.CheckStartTiming(this.props.Config.schedule_time, this.props.Config.end_time);
                const isSessionEnded = this.checkEndTimimg(this.props.Config.schedule_time, this.props.Config.end_time);
                const isNearingEnd = this.checkNearingEnd(this.props.Config.end_time);
                this.setState({
                    isSessionStarted: isSessionStarted,
                    isSessionEnded: isSessionEnded,
                    isNearingEnd: isNearingEnd,
                    currentTime: new Date()
                });
                if (isSessionStarted && isSessionEnded) {
                    clearInterval(this.intervalId);
                }
            }
        }, 500);
    }

    SetDuration(duration) {
        // Update session duration on the server --mayank

        // First, update the lead duration
        PostRequestWithHeaders({
            url: process.env.REACT_APP_API_BACKEND_API_URL + 'lead/UpdateDuration',
            body: {
                "session_id": this.props.roomId,
                "duration_minutes": duration / 60,
                "lead_id": this.props.ClientData.data._id
            }
        }).then(response => {
            console.log("Lead duration update response:", response);
        });

        // Then, update the session with the correct type
        if (this.props.Config) {
            // Get the current session type and project ID
            const sessionType = this.props.Config?.type || 'default';
            const projectId = this.props.Config?.project_id || null;

            // Determine if pixel streaming is active based on session type
            const isPixelActive = sessionType === 'pixel_streaming' || sessionType === 'lark';

            // Call the UpdateSession API with duration_minutes and lead_id
            UpdateSessionApi(
                this.props.roomId,
                projectId,
                sessionType,
                isPixelActive,
                duration / 60, // Convert duration to minutes
                this.props.ClientData.data._id // Pass the lead_id
            ).then(response => {
                console.log("Session update response:", response);
            }).catch(error => {
                console.error("Error updating session:", error);
            });
        }
    }

    toHumanReadableTime(isoString) {
        // Convert ISO string to human-readable date and time format --mayank
        const date = new Date(isoString);
        const formattedDate = `${date.toLocaleTimeString()} on ${date.toDateString()}`;
        return formattedDate;
    }

    CheckStartTiming(timestamp, endTimestamp) {
        // Check if the session has started but not ended --mayank
        const ScheduleDate = new Date(timestamp);
        const currtime = new Date();
        const endTime = new Date(endTimestamp)
        if (ScheduleDate <= currtime && currtime < endTime) {
            return true;
        }
        return false;
    }

    checkEndTimimg(timestamp, endTimestamp) {
        // Check if the session has ended --mayank
        const ScheduleDate = new Date(timestamp);
        const currtime = new Date();
        const endTime = new Date(endTimestamp)
        // console.log("Sessiondetais",ScheduleDate,)
        if (ScheduleDate < currtime && currtime >= endTime) {
            this.setState({ isSessionStarted: true });
            return true;
        }
        return false;
    }



    render() {
        if (!this.props.Config) {
            return <div>Loading...</div>;
        }
        

        // Check if project ID is undefined or type is 'default'
        const isDefaultOrNoProject = !this.props.Config?.project_id ||
                                    this.props.Config?.type === 'default';

        // If project ID is undefined or type is 'default', show the default video call interface
        if (isDefaultOrNoProject) {
            // Debug logging for screen sharing state
            console.log('Guest TypeSwitch - Debug Info:', {
                Peers: this.props.Peers,
                userVideoAudio: this.props.userVideoAudio,
                peersArray: this.props.Peers ? Object.values(this.props.Peers) : [],
                peersWithScreenSharing: this.props.Peers ? Object.values(this.props.Peers).filter(peer =>
                    peer && this.props.userVideoAudio && this.props.userVideoAudio[peer.peerID] &&
                    this.props.userVideoAudio[peer.peerID].screen
                ) : []
            });

            // Find any participant (including host) who is sharing their screen
            const sharingPeer = this.props.Peers && Object.values(this.props.Peers).find(peer =>
                peer && this.props.userVideoAudio && this.props.userVideoAudio[peer.peerID] &&
                this.props.userVideoAudio[peer.peerID].screen
            );

            // If someone is sharing, display it in the TypeSwitch area
            if (sharingPeer) {
                // Determine if this is the host or a guest
                const isHost = sharingPeer.extra && sharingPeer.extra.type === 'host';
                const sharingUserName = isHost ? 'Host' : sharingPeer.userName;

                return (
                    <div className="w-full h-full relative bg-black">
                        {/* Shared content in TypeSwitch area */}
                        <div className="absolute inset-0">
                            <video
                                className="w-full h-full object-contain bg-black ScreenSharingInTypeSwitch"
                                id={sharingPeer.peerID + "_SCREEN_VIDEO"}
                                autoPlay
                                playsInline
                                ref={(video) => {
                                    console.log('Guest TypeSwitch - Video ref callback:', {
                                        video: video,
                                        sharingPeer: sharingPeer,
                                        streams: sharingPeer.streams,
                                        stream0: sharingPeer.streams && sharingPeer.streams[0],
                                        peerID: sharingPeer.peerID
                                    });
                                    if (video && sharingPeer.streams && sharingPeer.streams[0]) {
                                        console.log('Guest TypeSwitch - Setting video srcObject:', sharingPeer.streams[0]);
                                        video.srcObject = sharingPeer.streams[0];
                                    } else {
                                        console.log('Guest TypeSwitch - Cannot set video srcObject - missing video or stream');
                                        // Retry after a short delay in case stream is not ready yet
                                        setTimeout(() => {
                                            if (video && sharingPeer.streams && sharingPeer.streams[0]) {
                                                console.log('Guest TypeSwitch - Retry: Setting video srcObject:', sharingPeer.streams[0]);
                                                video.srcObject = sharingPeer.streams[0];
                                            }
                                        }, 500);
                                    }
                                }}
                            />
                            {/* User name overlay */}
                            <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded">
                                {sharingUserName} is sharing their screen
                            </div>
                        </div>
                    </div>
                );
            }

            return (
                <div className="w-full h-full">
                    {/* The VideoCard components in SideBar will handle screen sharing display automatically */}
                </div>
            );
        }

        // Otherwise, render based on the project type
        switch (this.props.Config.type) {
            case "pixel_streaming": // Handle regular pixel streaming type
                return (
                    <>
                        {this.state.isSessionStarted ? (
                            <PixelStreaming SendCustomMessage={this.props.SendCustomMessage} SetMessage={this.props.SetMessage} Config={this.props.Config} roomId={this.props.roomId} SubscribeToCustom={this.props.SubscribeToCustom} />
                        ) : (
                            <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-50 flex items-center justify-center">
                                <div className="bg-[black]/40 bg-opacity-40 backdrop-blur p-6 rounded-lg shadow-md text-center">
                                    <h2 className="text-lg font-bold text-white mb-3">
                                        {this.state.isSessionEnded ? "Session is Over" : "Session Not Started Yet"}
                                    </h2>
                                    <p className="text-base text-white mb-3">
                                        {this.state.isSessionEnded
                                            ? "You're welcome to stay on the call, though the full experience may not be available at this time."
                                            : "The session has not begun. Please wait until the scheduled start time."}
                                    </p>
                                    {!this.state.isSessionEnded && (
                                        <p className="text-md text-white mb-3">
                                            Scheduled Start Time: {this.props.Config?.schedule_time && this.toHumanReadableTime(this.props.Config.schedule_time)}
                                        </p>
                                    )}
                                    <div className="flex justify-end"></div>
                                </div>
                            </div>
                        )}
                    </>
                )
            case "lark": // Handle LarkXR type
                return (
                    <>
                        {this.state.isSessionStarted ? (
                            <Lark 
                                SendCustomMessage={this.props.SendCustomMessage} 
                                SetMessage={this.props.SetMessage} 
                                Config={this.props.Config} 
                                roomId={this.props.roomId}  // Make sure this is being passed
                                SubscribeToCustom={this.props.SubscribeToCustom}
                                isNearingEnd={this.state.isNearingEnd} 
                            />
                        ) : (
                            <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-50 flex items-center justify-center">
                                <div className="bg-[black]/40 bg-opacity-40 backdrop-blur p-6 rounded-lg shadow-md text-center">
                                    <h2 className="text-lg font-bold text-white mb-3">
                                        {this.state.isSessionEnded ? "Session is Over" : "Session Not Started Yet"}
                                    </h2>
                                    <p className="text-base text-white mb-3">
                                        {this.state.isSessionEnded
                                            ? "You're welcome to stay on the call, though the full experience may not be available at this time."
                                            : "The session has not begun. Please wait until the scheduled start time."}
                                    </p>
                                    {!this.state.isSessionEnded && (
                                        <p className="text-md text-white mb-3">
                                            Scheduled Start Time: {this.props.Config?.schedule_time && this.toHumanReadableTime(this.props.Config.schedule_time)}
                                        </p>
                                    )}
                                    <div className="flex justify-end"></div>
                                </div>
                            </div>
                        )}
                    </>
                )
            case "ale":
                return (
                    <>
                        <AleGuest roomId={this.props.roomId} SubscribeToCustom={this.props.SubscribeToCustom} />
                    </>
                )
            default:
                return <></>
            }
    }
}

const mapStateTothisprops = state => {
    return {
        Config: state.Call.config,
        ClientData: state.Call.ClientData,
        SessionDetails: state.Sessions.sessions,
        Peers: state.Call.Peers,
        userVideoAudio: state.Call.userVideoAudio,

    }
}

const mapDispatchTothisprops = {
    ...HostActions,
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(TypeSwitch)